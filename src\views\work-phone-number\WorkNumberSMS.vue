<template>
    <div ref="mainContentRef" class="height-100 oa">
        <div ref="searchContentRef" class="b-margin-16 all-padding-16" style="background-color: #fff">
            <div class="all-padding-16">
                <SearchBox
                    :searchOptionKey="'WORK_NUMBER_SMS_LIST'"
                    @updateSearchParams="updateSearchParams"
                >
                </SearchBox>
            </div>
        </div>
        <div ref="tableContentRef" class="l-padding-16 t-padding-16 r-padding-16 table-content" style="background-color: #fff; box-sizing: border-box">
            <div class="back-color-white border-box flex-grow-1">
                <div class="t-margin-16">
                    <el-table
                        ref="tableListRef"
                        :data="tableData"
                        v-loading="tableLoading"
                        row-key="id"
                        header-row-class-name="tableHeader"
                        :style="{ 'min-height': tableHeight + 'px' }"
                    >
                        <template v-if="!tableLoading" #empty>
                            <div class="display-flex flex-column top-bottom-center" >
                                <img class="w-260 h-260" src="@/assets/images/customer-public-no-data.png" alt="暂无数据" />
                                <div class="font-first-title-unactive color-two-grey b-margin-16">暂无数据</div>
                            </div>
                        </template>
                        <el-table-column label="工作号" prop="telX" width="150"></el-table-column>
                        <el-table-column label="短信内容" prop="smsContent"></el-table-column>
                        <el-table-column label="接收时间" prop="smsDate" width="150"></el-table-column>
                    </el-table>
                </div>
                <el-affix target=".table-content" position="bottom" :offset="16" :z-index="2">
                    <div class="pagination-bar">
                        <el-pagination
                            v-model:currentPage="pageInfo.page"
                            v-model:page-size="pageInfo.size"
                            :total="pageInfo.total"
                            :page-sizes="[20, 40, 60, 100]"
                            layout="total, sizes, prev, pager, next, jumper"
                            @change="pageChange"
                        />
                    </div>
                </el-affix>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import type { IWorkNumberSMSParams, IWorkNumberSMSTableDataItem } from '@/types/worknumber'
import { useFixedActionBar } from '@/js/use-fixed-action-bar'
import aicService from '@/service/aicService'

const tableContentRef=ref<HTMLDivElement | null>(null)
useFixedActionBar(tableContentRef)
const tableHeight = ref(500)
const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value && actionBarContentRef.value) {
        tableHeight.value =
            mainContentRef.value.clientHeight -
            searchContentRef.value.clientHeight -
            actionBarContentRef.value.clientHeight -
            32 -
            16 -
            16 -
            16 -
            16 -
            16
    }
}

const pageInfo = reactive({ 
    page: 1, 
    size: 20, 
    total: 0 
})
const tableLoading = ref(false)
const updateSearchParams = (params: IWorkNumberSMSParams) => {
    queryParams.value = params
    search()
}
const queryParams = ref<IWorkNumberSMSParams>({
    page:1,
    size:20,
})
const pageChange = (currentPage: number, size: number) => {
    queryParams.value.page = currentPage
    queryParams.value.size = size
    search()
}
const tableData = ref<IWorkNumberSMSTableDataItem[]>([])
const search = () => {
    tableLoading.value = true
    queryParams.value.page = pageInfo.page
    queryParams.value.size = pageInfo.size
    aicService.WorkNumberSMSList(queryParams.value).then((res) => {
        tableData.value = res.records
        pageInfo.total = res.total
    }).finally(() => {
        tableLoading.value = false
    })
}

onMounted(() => {
    getTableHeight()
    search()
})
</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';
.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}
</style>
