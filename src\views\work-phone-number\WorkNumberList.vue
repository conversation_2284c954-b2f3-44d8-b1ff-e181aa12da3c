<template>
    <div ref="mainContentRef" class="height-100 oa">
        <div ref="searchContentRef" class="b-margin-16 all-padding-16" style="background-color: #fff">
            <div class="all-padding-16">
                <SearchBox
                    :searchOptionKey="searchOptionKey"
                    @updateSearchParams="updateSearchParams"
                    :customConfig="searchConfig"
                >
                </SearchBox>
            </div>
        </div>
        <div
            ref="tableContentRef"
            class="l-padding-16 t-padding-16 r-padding-16 table-content"
            style="background-color: #fff; box-sizing: border-box"
        >
            <div class="back-color-white border-box flex-grow-1">
                <el-table
                    ref="tableListRef"
                    :data="tableData"
                    v-loading="tableLoading"
                    row-key="id"
                    header-row-class-name="tableHeader"
                    :style="{ 'min-height': tableHeight + 'px' }"
                >
                    <template v-if="!tableLoading" #empty>
                        <div class="display-flex flex-column top-bottom-center">
                            <img class="w-260 h-260" src="@/assets/images/customer-public-no-data.png" alt="暂无数据" />
                            <div class="font-first-title-unactive color-two-grey b-margin-16">暂无数据</div>
                        </div>
                    </template>
                    <el-table-column label="办税员号码" prop="telB"></el-table-column>
                    <el-table-column label="绑定状态" prop="authStatus">
                        <template #default="scope">
                            <el-button type="primary" text bg v-if="scope.row.authStatus === '0'">未绑定</el-button>
                            <el-button type="info" text bg v-if="scope.row.authStatus === '1'">实名认证中</el-button>
                            <el-button type="success" text bg v-if="scope.row.authStatus === '2'">实名认证成功</el-button>
                            <el-button type="danger" text bg v-if="scope.row.authStatus === '3'">超过7天未实名成功</el-button>
                            <el-button type="info" text bg v-if="scope.row.authStatus === '4'">待解绑</el-button>
                            <el-button type="danger" text bg v-if="scope.row.authStatus === '5'">解绑失败</el-button>
                            <el-button type="success" text bg v-if="scope.row.authStatus === '6'">解绑成功</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="工作号" prop="telX"></el-table-column>
                    <el-table-column v-if="isAdmin" label="所属租户" prop="tenantName"></el-table-column>
                    <el-table-column label="操作" min-width="100">
                        <template #default="scope">
                            <div class="display-flex gap-12">
                                <div
                                    v-if="scope.row.authStatus === '0' || scope.row.authStatus === '6'"
                                    @click="bindPhone(scope.row)"
                                    class="color-primary pointer"
                                >
                                    绑定工作号
                                </div>
                                <div
                                    v-if="scope.row.authStatus !== '0' && scope.row.authStatus !== '6' " 
                                    class="color-red pointer"
                                    @click="unbindPhone(scope.row)"
                                >
                                    解绑工作号
                                </div>
                                <div
                                    v-if="scope.row.authStatus !== '0' && scope.row.authStatus !== '6'"
                                    class="color-primary pointer"
                                    @click="taxerConfig(scope.row)"
                                >
                                    办税员配置
                                </div>
                                <div
                                    v-if="scope.row.authStatus !== '0' && scope.row.authStatus !== '6'"
                                    class="color-primary pointer"
                                    @click="bindCompany(scope.row)"
                                >
                                    绑定企业
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <el-affix v-show="!tableLoading" target=".table-content" position="bottom" :offset="16" :z-index="2">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.size"
                        :total="pageInfo.total"
                        :page-sizes="[20, 40, 60, 100]"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>
        </div>
    </div>
    <!-- 绑定工作号弹窗 -->
    <el-dialog v-model="bindVisible" title="绑定工作号" @close="handleBindClose" width="500px">
        <div class="display-flex flex-column gap-8">
            <div class="display-flex flex-column gap-8">
                <div class="display-flex top-bottom-center gap-12">
                    <div class="color-red">*</div>
                    <div>工作号</div>
                </div>
                <el-input v-model="bindWorkNumber" disabled></el-input>
            </div>
            <div class="display-flex flex-column gap-8">
                <div class="display-flex top-bottom-center gap-12">
                    <div class="color-red">*</div>
                    <div>办税员手机号</div>
                </div>
                <el-input v-model="bindTaxerPhone" @input="validatePhone"></el-input>
                <div v-if="!phoneValid || bindTaxerPhone === ''" class="color-red font-12">{{ errMsg }}</div>
            </div>
            <div class="t-margin-16 display-flex flex-end">
                <el-button class="l-margin-12 all-padding-16" @click="handleBindClose">取消</el-button>
                <el-button 
                    :loading="confirmLoading" 
                    type="primary" 
                    class="all-padding-16" 
                    @click="submitBind"  
                >提交</el-button
                >
            </div>
        </div>
    </el-dialog>
    <!-- 办税员配置弹窗 -->
    <el-dialog
        v-model="taxerConfigVisible"
        title="办税员配置"
        @close="resetFormdata(ruleFormRef)"
        width="620px"
    >
        <div class="display-flex flex-column gap-8">
            <el-form
                ref="ruleFormRef"
                :inline="true"
                :model="formData" 
                class="demo-form-inline"
                :rules="rules"
            >
                <el-form-item>
                    <div class="display-flex flex-column gap-4">
                        <div class="display-flex top-bottom-center gap-4">
                            <span class="color-red">*</span>
                            <span class="">工作号</span>
                        </div>
                        <el-input v-model="formData.workNumber" disabled></el-input>
                    </div>
                </el-form-item>
                <el-form-item prop="name">
                    <div class="display-flex flex-column gap-4">
                        <div class="display-flex top-bottom-center gap-4">
                            <span class="color-red">*</span>
                            <span class="">办税员姓名</span>
                        </div>
                        <el-input v-model="formData.name" clearable></el-input>
                    </div>
                </el-form-item>
                <el-form-item>
                    <div class="display-flex flex-column gap-4">
                        <div class="display-flex top-bottom-center gap-4">
                            <span class="color-red">*</span>
                            <span class="">办税员手机号</span>
                        </div>
                        <el-input v-model="formData.phoneNumber" disabled></el-input>
                    </div>
                </el-form-item>
                <el-form-item prop="idCard">
                    <div class="display-flex flex-column gap-4">
                        <div class="display-flex top-bottom-center gap-4">
                            <span class="color-red">*</span>
                            <span class="">办税员身份证号</span>
                        </div>
                        <el-input v-model="formData.idCard" clearable></el-input>
                    </div>
                </el-form-item>
                <el-form-item prop="loginCode">
                    <div class="display-flex flex-column gap-4">
                        <div class="display-flex top-bottom-center gap-4">
                            <span class="color-red">*</span>
                            <span class="">办税员密码</span>
                        </div>
                        <el-input v-model="formData.loginCode" clearable></el-input>
                    </div>
                </el-form-item>
                <el-form-item prop="sflx">
                    <div class="display-flex flex-column gap-4">
                        <div class="display-flex top-bottom-center gap-4">
                            <span class="color-red">*</span>
                            <span class="">身份类型</span>
                        </div>
                        <el-select v-model="formData.sflx" clearable>
                            <el-option label="办税员" value="BSY"></el-option>
                            <el-option label="开票员" value="KPY"></el-option>
                            <el-option label="财务负责人" value="CWFZR"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
            </el-form>
            <div class="t-margin-16 display-flex flex-end">
                <el-button class="l-margin-12 all-padding-16" @click="resetFormdata(ruleFormRef)">取消</el-button>
                <el-button 
                    :loading="confirmLoading" 
                    type="primary" 
                    class="all-padding-16" 
                    @click="submitTaxerConfig(ruleFormRef)"  
                >提交</el-button
                >
            </div>
        </div>
    </el-dialog>
    <!-- 绑定企业弹窗 -->
    <BindCompanyList :visible="bindCompanyVisible" @update:visible="handleBindCompanyClose" :workNumberInfo="workNumberInfo" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useFixedActionBar } from '@/js/use-fixed-action-bar'
import { useStore } from 'vuex'
import systemService from '@/service/systemService'
import type { IWorkNumberListParams, IWorkNumberListTableDataItem, IBindWorkNumberParams, IBindTaxOfficerParams } from '@/types/worknumber'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import BindCompanyList from './components/BindCompanyDialog.vue'
import aicService from '@/service/aicService'

type CustomConfig = {
    [key: string]: Array<{
        label: string
        value: string
    }>
}
const searchConfig = ref<CustomConfig>()
const store = useStore()
const isAdmin = computed(() => {
    let user = store.state.user?.account?.user || []
    if (user.role.includes('admin') || user.role.includes('yunwei')) {
        return true
    } else {
        return false
    }
})

const tableContentRef = ref<HTMLDivElement | null>(null)
useFixedActionBar(tableContentRef)
const tableHeight = ref(500)
const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value && actionBarContentRef.value) {
        tableHeight.value =
            mainContentRef.value.clientHeight -
            searchContentRef.value.clientHeight -
            actionBarContentRef.value.clientHeight -
            32 -
            16 -
            16 -
            16 -
            16 -
            16
    }
}

const pageInfo = reactive({
    page: 1,
    size: 20,
    total: 0,
})

const tableLoading = ref(false)
const tableData = ref<IWorkNumberListTableDataItem[]>([])
const queryParams = ref<IWorkNumberListParams>({
    page: 1,
    size: 20,
})
const updateSearchParams = (params: IWorkNumberListParams) => {
    queryParams.value = params
    queryParams.value.tenantId = params.tenantId?.join(',')
    search()
}
const pageChange = (currentPage: number, size: number) => {
    queryParams.value.page = currentPage
    queryParams.value.size = size
    search()
}

const search = () => {
    tableLoading.value = true
    queryParams.value.page = pageInfo.page
    queryParams.value.size = pageInfo.size
    // console.log('queryParams', queryParams.value)
    aicService.workNumberList(queryParams.value).then((response) => {
        // console.log('search', response)
        tableData.value = response.records
        pageInfo.total = response.total
    }).finally(() => {
        tableLoading.value = false
    })
}
const confirmLoading = ref(false)
// 绑定相关
const bindVisible = ref(false)
const bindWorkNumber = ref('')
const bindTaxerPhone = ref('')
const phoneValid = ref(true)
const bindWorkNumberParams = ref<IBindWorkNumberParams>({
    bindType: 1,
    workPhone: '',
    taxOfficerPhone: '',
})
const errMsg = computed(() => {
    if (!phoneValid.value) {
        return '请输入正确的手机号码'
    }
    if (bindTaxerPhone.value === '') {
        return '请输入办税员手机号'
    }
    return ''
})
const validatePhone = () => {
    const phoneRegex = /^1[3-9]\d{9}$/
    phoneValid.value = phoneRegex.test(bindTaxerPhone.value)
}
const bindPhone = (row: IWorkNumberListTableDataItem) => {
    // console.log('bindPhone', row)
    bindVisible.value = true
    bindWorkNumber.value = row.telX
    bindWorkNumberParams.value.workPhone = row.telX
}
const handleBindClose = () => {
    bindTaxerPhone.value = ''
    bindVisible.value = false
    confirmLoading.value = false
}
const submitBind = () => {
    if (!phoneValid.value || bindTaxerPhone.value === '') return
    confirmLoading.value = true
    bindWorkNumberParams.value.taxOfficerPhone = bindTaxerPhone.value
    aicService.workNumberBind(bindWorkNumberParams.value).then((response) => {
        // console.log('submitBind', response)
        if(response.success){
            ElMessage.success('绑定成功')
            bindVisible.value = false
            search()
        }else{
            ElMessage.error(response.errMsg)
        }
    }).finally(() => {
        confirmLoading.value = false
    })
}

// 解绑相关
const unbindWorkNumberParams = ref<IBindWorkNumberParams>({
    bindType: 2,
    xhId:''
})
const unbindPhone = (row: IWorkNumberListTableDataItem) => {
    unbindWorkNumberParams.value.xhId = row.id
    // console.log('unbindPhone', row)
    ElMessageBox.confirm('是否确认解绑?',
        '确认',
        {
            confirmButtonText: '解绑',
            cancelButtonText: '取消',
            type: 'warning',
        }).then(() => {
        aicService.workNumberBind(unbindWorkNumberParams.value).then((response) => {
            // console.log('submitUnbind', response)
            if(response.success){
                ElMessage.success('解绑成功')
                search()
            }else{
                ElMessage.error(response.errMsg)
            }
        })
    })
}

// 办税员配置相关
const taxerConfigVisible = ref(false)
const taxerConfig = (row: IWorkNumberListTableDataItem) => {
    // console.log('taxerConfig', row)
    taxerConfigVisible.value = true
    formData.phoneNumber = row.telB
    formData.workNumber = row.telX
    aicService.getTaxOfficerInfo({telX:row.telX,channelId:row.channelId}).then((response) => {
        console.log('getTaxOfficerInfo', response)  
    })
}
const formData = reactive({
    workNumber:'',
    name:'',
    phoneNumber:'',
    idCard:'',
    loginCode:'',
    sflx:'',
})

const resetFormdata = (formEl: FormInstance | undefined) => {
    console.log('formEl',formEl)
    if (formEl) {
        formEl.resetFields()
    }
    clearFormData()
    handleTaxerConfigClose()
}
const clearFormData = () => {
    if (ruleFormRef.value) {
        ruleFormRef.value.clearValidate()
    }
    Object.assign(formData, {
        workNumber: '',
        name: '',
        phoneNumber: '',
        idCard: '',
        loginCode: '',
        sflx: '',
    })
}
const handleTaxerConfigClose = () => {
    taxerConfigVisible.value = false
}
interface RuleForm {
  workNumber: string
  name: string
  phoneNumber: string
  idCard: string
  loginCode: string
  sflx: boolean
}
const ruleFormRef = ref<FormInstance>()
const rules = reactive<FormRules<RuleForm>>({
    name: [{required: true, message: '请输入办税员姓名', trigger: 'blur'}],
    idCard: [
        {required: true, message: '请输入办税员身份证号', trigger: 'blur'},
        {}
    ],
    loginCode: [{required: true, message: '请输入办税员密码', trigger: 'blur'}],
    sflx: [{required: true, message: '请选择办税员身份', trigger: 'blur'}],
})
const bindTaxOfficerParams = ref<IBindTaxOfficerParams>({
    bsyxm:'',
    bsysjhm:'',
    sflX:'',
    bsysfzhm:'',
    bsymm:'',
    telX:''
})
const submitTaxerConfig = async (formEl: FormInstance | undefined) => {
    confirmLoading.value = true
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            // console.log('提交表单数据',formData)
            bindTaxOfficerParams.value.bsyxm = formData.name
            bindTaxOfficerParams.value.bsysjhm = formData.phoneNumber
            bindTaxOfficerParams.value.sflX = formData.sflx
            bindTaxOfficerParams.value.bsysfzhm = formData.idCard
            bindTaxOfficerParams.value.bsymm = formData.loginCode
            bindTaxOfficerParams.value.telX = formData.workNumber
            // console.log('bindTaxOfficerParams', bindTaxOfficerParams.value)
            aicService.taxOfficerConfig(bindTaxOfficerParams.value).then((response) => {
                // console.log('submitTaxerConfig', response)
                if(response.success){
                    ElMessage.success('操作成功')
                    clearFormData()
                    handleTaxerConfigClose()
                }else{
                    ElMessage.error(response.errMsg)
                }
            }).finally(() => {
                confirmLoading.value = false
            })
        } else {
            console.log('error submit!', fields)
        }
    })
}
// 绑定企业相关 
const workNumberInfo = ref<IWorkNumberListTableDataItem>()
const bindCompanyVisible = ref(false)
const bindCompany = (row: IWorkNumberListTableDataItem) => {
    // console.log('bindCompany', row)
    workNumberInfo.value = row
    bindCompanyVisible.value = true
}
const handleBindCompanyClose = () => {
    bindCompanyVisible.value = false
}

const searchOptionKey = ref('WORK_PHONE_SEARCH_OPTIONS')
onMounted(() => {
    getTableHeight()
    search()
    if (isAdmin.value) {
        searchOptionKey.value = 'WORK_PHONE_SEARCH_OPTIONS_FORADMIN'
        systemService.tenantList().then((response) => {
            searchConfig.value = {
                ...searchConfig.value,
                tenantIds: response.map((item) => ({
                    label: item.name,
                    value: item.id,
                })),
            }
        })
    }
})
</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';
.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}

.demo-form-inline .el-input {
  --el-input-width: 250px;
}

.demo-form-inline .el-select {
  --el-select-width: 250px;
}
</style>
