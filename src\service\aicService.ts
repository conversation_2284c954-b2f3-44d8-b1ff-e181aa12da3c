import http from '@/axios'
import type { ICommonResponse } from '@/types/axios'
import type {
    IAicConditionData,
    IAicConditionDataRequest,
    IAicNormalSearchRules,
    ISearchListParams,
    ModelGetCategoryListResponse,
    AddProjectParams,
    SaveTempleteParams,
    UpdateTemplateParams,
    GsGetPersonEnterpriseRelationsParams,
    GsGetPersonEnterpriseRelationsResponse,
    ICenterEntParams,
    ICenterEntResponse,
    ISearchCopyTemplateParams,
    ISearchGetEntTemplateResponse,
    IGetGsGetDetailParams,
    IGetGsGetDetailResponse,
} from '@/types/aic'
import type {
    ISearchGSInfoParams,
    IGetModelCategoryResponse,
    IGetCompanyByCodeParams,
    SearchCompanyGsInfoResponse,
    CompanyBaseInfo,
    GetCompanyDataParams,
    GetCompanyContactResponse,
    ISearchAdvancedSearchParams,
    ISearchAdvancedSearchResponse,
    ISearchGetCategoryParams,
    ISearchGetCategoryResponse,
    ISearchGetTemplateParams,
    ISearchGetTemplateResponse,
    ISearchBidAndFactoryParams,
    ISearchBidAndFactoryResponse,
    ISearchBidProjectResponse,
    ISearchBidCompanyResponse,
    IGetAnuualReportDetailParams,
} from '@/types/company'
import type { IPageUserItem } from '@/types/user'
import type { IHighSearchRules } from '@/types/model'
import type {
    IApplyWorkNumberParams,
    IApplyNumberListParams,
    IApplyNumberListTableData,
    IWorkNumberListParams,
    IWorkNumberListTableData,
    IBindWorkNumberParams,
    IBindTaxOfficerParams,
    IBindCompanyParams,
    IGetTaxOfficerInfoParams,
    IGetTaxOfficerInfoData,
    IBindCompanyTableParams,
    IBindCompanyTableData,
    IWorkNumberSMSParams,
    IWorkNumberSMSTableData,
} from '@/types/worknumber'

export default {
    conditionGetData(body?: IAicConditionDataRequest): Promise<IAicConditionData> {
        return http.get(`/api/zhenqi-aic/condition/get-data`, {
            params: body,
        })
    },
    conditionGetUser(): Promise<IPageUserItem[]> {
        return http.get(`/api/zhenqi-aic/condition/get-user`)
    },
    searchEnterprise(data: IGetCompanyByCodeParams): Promise<ISearchAdvancedSearchResponse> {
        return http.post(`/api/zhenqi-aic/search/enterprise`, data, {
            hideError: true,
            repeatCancel: true,
        })
    },
    gsInfo(data: ISearchGSInfoParams): Promise<SearchCompanyGsInfoResponse> {
        return http.get(`/api/zhenqi-aic/gs/info`, {
            params: data,
        })
    },
    gsGetCompanyBaseInfo(data: GetCompanyDataParams): Promise<CompanyBaseInfo> {
        return http.get(`/api/zhenqi-aic/gs/get-company-base-info`, {
            params: data,
        })
    },
    gsGetContacts(data: GetCompanyDataParams): Promise<GetCompanyContactResponse> {
        return http.get(`/api/zhenqi-aic/gs/get-contacts`, {
            params: data,
        })
    },
    conditionGetDetailModel(): Promise<IGetModelCategoryResponse[]> {
        return http.get(`/api/zhenqi-aic/condition/get-detail-model`)
    },
    conditionGetInfo(body: { searchType: string }): Promise<Array<IHighSearchRules>> {
        return http.get(`/api/zhenqi-aic/condition/get-info`, {
            params: body,
        })
    },
    conditionGetInfoForNomal(body: { searchType: string }): Promise<IAicNormalSearchRules[]> {
        return http.get(`/api/zhenqi-aic/condition/get-info`, {
            params: body,
        })
    },
    searchAdvancedSearch(data: ISearchAdvancedSearchParams): Promise<ISearchAdvancedSearchResponse> {
        return http.post(`/api/zhenqi-aic/search/advanced-search`, data, {
            hideError: true,
        })
    },

    searchGetTemplate(data: ISearchGetTemplateParams): Promise<ISearchGetTemplateResponse> {
        return http.get(`/api/zhenqi-aic/search/get-template`, {
            params: data,
            hideError: true,
        })
    },
    searchFactory(data: ISearchBidAndFactoryParams): Promise<ISearchBidAndFactoryResponse> {
        return http.post('/api/zhenqi-aic/search/factory', data, {
            hideError: true,
        })
    },
    searchTenderProject(data: ISearchBidAndFactoryParams): Promise<ISearchBidProjectResponse> {
        return http.post('/api/zhenqi-aic/search/tender-project', data, {
            hideError: true,
            repeatCancel: true,
        })
    },
    searchScene(data: ISearchBidAndFactoryParams): Promise<ISearchBidCompanyResponse> {
        return http.post('/api/zhenqi-aic/search/scene', data, {
            hideError: true,
            repeatCancel: true,
        })
    },
    modelGetCategoryList(data: ISearchListParams): Promise<ModelGetCategoryListResponse> {
        return http.get(`/api/zhenqi-aic/model/get-category-list`, {
            params: data,
        })
    },
    modelGetCategory(data: ISearchGetCategoryParams): Promise<ISearchGetCategoryResponse> {
        return http.get(`/api/zhenqi-aic/model/get-category`, {
            params: data,
            hideError: true,
        })
    },
    // 新增项目分类
    modelNewCategory(data: AddProjectParams): Promise<ICommonResponse> {
        return http.post('/api/zhenqi-aic/model/new-category', data, {
            hideError: true,
        })
    },
    // 修改项目分类节点信息
    modelUpdateCategory(data: AddProjectParams): Promise<ICommonResponse> {
        return http.post('/api/zhenqi-aic/model/update-category', data, {
            hideError: true,
        })
    },
    // 删除项目分类
    modelDeleteTopCategory(categoryId: string): Promise<ICommonResponse> {
        return http.delete('/api/zhenqi-aic/model/delete-top-category', {
            params: { categoryId },
            hideError: true,
        })
    },
    // 删除节点
    modelDeleteCategory(categoryId: string): Promise<ICommonResponse> {
        return http.delete('/api/zhenqi-aic/model/delete-category', {
            params: { categoryId },
            hideError: true,
        })
    },
    // 赋值项目
    modelCopyCategory(categoryId: string): Promise<ICommonResponse> {
        return http.post(
            '/api/zhenqi-aic/model/copy-category',
            { categoryId: categoryId },
            {
                hideError: true,
            }
        )
    },
    searchDeleteTemplate(templateId: string): Promise<ICommonResponse> {
        return http.delete('/api/zhenqi-aic/search/delete-template', {
            params: { templateId },
        })
    },
    modelLoadCategory(data: { categoryId?: string }): Promise<ISearchGetCategoryResponse> {
        return http.get(`/api/zhenqi-aic/model/load-category`, {
            params: data,
            hideError: true,
        })
    },
    searchSaveTemplate(data: SaveTempleteParams): Promise<ICommonResponse> {
        return http.post('/api/zhenqi-aic/search/save-template', data)
    },
    searchUpdateTemplate(data: UpdateTemplateParams): Promise<ICommonResponse> {
        return http.put('/api/zhenqi-aic/search/update-template', data)
    },
    // 分享模板
    searchCopyTemplate(data: ISearchCopyTemplateParams): Promise<ICommonResponse> {
        return http.post('/api/zhenqi-aic/search/copy-template', data)
    },
    searchGetEntTemplate( data: ISearchListParams ): Promise<ISearchGetEntTemplateResponse> {
        return http.get('/api/zhenqi-aic/search/get-ent-template', {
            params: data,
            hideError: true,
        })
    },
    gsGetPersonEnterpriseRelations(
        data: GsGetPersonEnterpriseRelationsParams
    ): Promise<GsGetPersonEnterpriseRelationsResponse> {
        return http.get('/api/zhenqi-aic/gs/get-person-enterprise-relations', {
            params: data,
            hideError: true,
        })
    },
    gsGetGetAppointDetailModel(data: { name: string }): Promise<{ data: IGetModelCategoryResponse }> {
        return http.get('/api/zhenqi-aic/condition/get-appoint-detail-model', {
            params: data,
            hideError: true,
        })
    },
    searchCenterEnt(data: ICenterEntParams): Promise<ICenterEntResponse> {
        return http.post('/api/zhenqi-aic/search/center-ent', data, {
            hideError: true,
        })
    },
    searchStatistics(data: ICenterEntParams): Promise<ICenterEntResponse> {
        return http.post('/api/zhenqi-aic/search/statistics', data, {
            hideError: true,
        })
    },
    // 获取招投标详情
    searchGetTenderContents(data: { ossId: string }): Promise<{data:string}> {
        return http.get(`/api/zhenqi-aic/search/get-tender-contents`, {
            params: data,
        })
    },
    searchLastEnt(data: IGetCompanyByCodeParams): Promise<ISearchAdvancedSearchResponse> {
        return http.post(`/api/zhenqi-aic/search/last-ent`, data, {
            hideError: true,
            repeatCancel: true,
        })
    },
    // 获取年报详情
    getAnnualReportDetail(data:IGetAnuualReportDetailParams): Promise<ICommonResponse> {
        return http.get(`/api/zhenqi-aic/gs/get-annual-report-detail`, {
            params: data,
            hideError: true,
        })
    },
    getGsGetDetail(data:IGetGsGetDetailParams): Promise<IGetGsGetDetailResponse> {
        return http.get(`/api/zhenqi-aic/gs/get-detail`, {
            params: data,
            hideError: true,
        })
    },
    // 申请小号
    applyWorkNumber(data:IApplyWorkNumberParams):Promise<ICommonResponse>{
        return http.post(`/api/zhenqi-aic/work/number/applyWorkNumber`,data,{
            hideError: true,
        })
    },
    // 小号申请订单列表
    applyWorkNumberList(data:IApplyNumberListParams):Promise<IApplyNumberListTableData>{
        return http.post(`/api/zhenqi-aic/work/number/applyWorkNumberList`, data)
    },
    // 小号订单详情/列表
    workNumberList(data:IWorkNumberListParams):Promise<IWorkNumberListTableData>{
        return http.post(`/api/zhenqi-aic/work/number/workNumberList`,data)
    },
    // 小号绑定/解绑/换绑
    workNumberBind(data:IBindWorkNumberParams):Promise<ICommonResponse>{
        return http.post(`/api/zhenqi-aic/work/number/bindNum`,data,{
            hideError: true,
        })
    },
    // 办税员配置
    taxOfficerConfig(data:IBindTaxOfficerParams):Promise<ICommonResponse>{
        return http.post(`/api/zhenqi-aic/work/number/account/save`,data,{
            hideError: true,
        })
    },
    // 绑定企业
    bindEnterprise(data:IBindCompanyParams):Promise<ICommonResponse>{
        return http.post(`/api/zhenqi-aic/work/number/bindBsy`,data,{
            hideError: true,
        })
    },
    // 解绑企业
    unbindEnterprise(data:IBindCompanyParams):Promise<ICommonResponse>{
        return http.post(`/api/zhenqi-aic/work/number/unbindBsy`,data,{
            hideError: true,
        })
    },
    // 查询办税员信息
    getTaxOfficerInfo(data:IGetTaxOfficerInfoParams):Promise<IGetTaxOfficerInfoData>{
        return http.post(`/api/zhenqi-aic/work/number/account/detail`,data,{
            hideError: true,
        })
    },
    // 查询绑定企业列表
    bsyBindEntList(data:IBindCompanyTableParams):Promise<IBindCompanyTableData>{
        return http.post(`/api/zhenqi-aic/work/number/bsyBindEntList`,data)
    },
    // 工作号短信列表
    WorkNumberSMSList(data:IWorkNumberSMSParams):Promise<IWorkNumberSMSTableData>{
        return http.post(`/api/zhenqi-aic/work/number/smsList`,data)
    },
}
