<template>
    <div ref="mainContentRef" style="background-color: #f7f7f7;">
        <div ref="searchContentRef" class="b-margin-16 all-padding-16" style="background-color: #fff">
            <div class="all-padding-16">
                <SearchBox
                    :searchOptionKey="searchOptionKey"
                    @updateSearchParams="updateSearchParams"
                    :customConfig="searchConfig"
                >
                </SearchBox>
            </div>
        </div>
        <div ref="tableContentRef" class="l-padding-16 t-padding-16 r-padding-16 table-content" style="background-color: #fff; box-sizing: border-box">
            <div class="back-color-white border-box flex-grow-1">
                <!-- 工具条 -->
                <div v-if="!isAdmin && permissionService.isApplyWorkNumberPermitted()" ref="actionBarContentRef" class="display-flex top-bottom-center action-bar justify-end b-margin-16">
                    <el-button type="primary" @click="applyWorkNumber()">申请小号</el-button>
                </div>
                <div class="t-margin-16">
                    <el-table
                        ref="tableListRef"
                        :data="tableData"
                        v-loading="tableLoading"
                        row-key="id"
                        header-row-class-name="tableHeader"
                        :style="{ 'min-height': tableHeight + 'px' }"
                    >
                        <template v-if="!tableLoading" #empty>
                            <div class="display-flex flex-column top-bottom-center" >
                                <img class="w-260 h-260" src="@/assets/images/customer-public-no-data.png" alt="暂无数据" />
                                <div class="font-first-title-unactive color-two-grey b-margin-16">暂无数据</div>
                            </div>
                        </template>
                        <el-table-column label="订单ID" prop="transId"></el-table-column>
                        <el-table-column label="申请个数" prop="bsxhNum"></el-table-column>
                        <el-table-column label="申请时间" prop="createTime"></el-table-column>
                        <el-table-column label="申请状态" prop="transStatus">
                            <template #default="scope">
                                <el-tag type="primary" text bg v-if="scope.row.transStatus === 0">审核中</el-tag>
                                <el-tag type="primary" text bg v-if="scope.row.transStatus === 1">申请中</el-tag>
                                <el-tag type="success" text bg v-if="scope.row.transStatus === 2">申请通过</el-tag>
                                <el-tag type="danger" text bg v-if="scope.row.transStatus === 3">审核驳回</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="备注" prop="remark"></el-table-column>
                        <el-table-column v-if="isAdmin" label="所属租户" prop="tenantName"></el-table-column>
                        <el-table-column label="操作">
                            <template #default="scope">
                                <div v-if="scope.row.transStatus === 2" @click="toDetail(scope.row)" class="color-primary pointer">详情</div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-affix v-show="!tableLoading" target=".table-content" position="bottom" >
                        <div class="pagination-bar">
                            <el-pagination
                                v-model:currentPage="pageInfo.page"
                                v-model:page-size="pageInfo.size"
                                :total="pageInfo.total"
                                :page-sizes="[20, 40, 60, 100]"
                                layout="total, sizes, prev, pager, next, jumper"
                                @change="pageChange"
                            />
                        </div>
                    </el-affix>
                </div>
            </div>
        </div> 
    </div>
    <el-dialog
        v-model="applyVisible"
        title="申请小号"
        width="500px"
        @close="handleApplyClose"
    >
        <div class="">
            <div class="display-flex flex-column gap-8">
                <div class="display-flex top-bottom-center gap-12">
                    <div class="color-red">*</div>
                    <div>选择数量</div>
                </div>
                <el-input-number v-model="phoneCount"  />
            </div>
            <div class="display-flex flex-end">
                <el-button
                    class="l-margin-12 all-padding-16"
                    @click="handleApplyClose"
                >取消</el-button>
                <el-button
                    :loading="confirmLoading"
                    type="primary"
                    class="all-padding-16"
                    @click="submit"
                >提交</el-button>
            </div>
        </div>
    </el-dialog>
    <el-dialog
        v-model="phoneNumberVisible"
        title="详情"
    >
        <div class="display-flex flex-column gap-12">
            <div>租户小号</div>
            <div class="border-radius-8 all-padding-8" style="min-height:100px; border:1px solid #ccc;" >
                {{ workNumberList }}
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts" setup>
import SearchBox from '@/components/common/SearchBox.vue'
import { ref, onMounted, computed, reactive } from 'vue'
import { useStore } from 'vuex'
import systemService from '@/service/systemService'
import type { IApplyNumberListParams, IApplyNumberListTableDataItem } from '@/types/worknumber'
import { useFixedActionBar } from '@/js/use-fixed-action-bar'
import { ElMessage } from 'element-plus'
import aicService from '@/service/aicService'
import permissionService from '@/service/permissionService'

const store = useStore()
const isAdmin = computed(() => {
    let user = store.state.user?.account?.user || []
    if (user.role.includes('admin') || user.role.includes('yunwei')) {
        return true
    } else {
        return false
    }
})

const tableContentRef=ref<HTMLDivElement | null>(null)
useFixedActionBar(tableContentRef)
const tableHeight = ref(500)
const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value && actionBarContentRef.value) {
        tableHeight.value =
            mainContentRef.value.clientHeight -
            searchContentRef.value.clientHeight -
            actionBarContentRef.value.clientHeight -
            32 -
            16 -
            16 -
            16 -
            16 -
            16
    }
}
const pageInfo = reactive({ 
    page: 1, 
    size: 20, 
    total: 0 
})
type CustomConfig = {
    [key: string]: Array<{
        label: string
        value: string
    }>
}
const searchConfig = ref<CustomConfig>()


const applyWorkNumber = () => {
    console.log('applyWorkNumber')
    applyVisible.value = true
}

const workNumberList = ref<string[]>()
const toDetail = (row:IApplyNumberListTableDataItem) => {
    console.log('toDetail',row)
    phoneNumberVisible.value = true
    aicService.workNumberList({page:1,size:999,transId:row.transId}).then((res) => {
        console.log('workNumberList',res)
        workNumberList.value = res.records.map((item) => item.telX)
    })
}

const updateSearchParams = (params: IApplyNumberListParams) => {
    queryParams.value = params
    // 判断并处理时间区间
    if (params.createTime?.[0]) {
        params.createTime[0] = `${params.createTime[0]} 00:00:00`
    }
    if (params.createTime?.[1]) {
        params.createTime[1] = `${params.createTime[1]} 23:59:59`
    }
    // console.log('params.createTime',params.createTime?.[0],params.createTime?.[1])
    console.log('queryParams', queryParams.value)
    search()
}

const tableData = ref<IApplyNumberListTableDataItem[]>([])
const searchOptionKey = ref('NUMBER_APPLY_SEARCH_OPTIONS')
const tableLoading = ref(false)
const queryParams = ref<IApplyNumberListParams>({
    page:1,
    size:20,
})
const pageChange = (currentPage: number, pageSize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.size = pageSize
    search()
}
const search = () => {
    tableLoading.value = true
    queryParams.value.page = pageInfo.page
    queryParams.value.size = pageInfo.size

    aicService.applyWorkNumberList(queryParams.value).then((res) => {
        console.log('tableData', res)
        tableData.value = res.records
        pageInfo.total = res.total
    }).finally(() => {
        tableLoading.value = false
    })
}

const applyVisible = ref(false)
const handleApplyClose = () => {
    applyVisible.value = false
}
// 申请的小号数量
const phoneCount = ref(1)
const confirmLoading = ref(false)
const submit = () => {
    confirmLoading.value = true
    // console.log('提交',phoneCount.value)
    aicService.applyWorkNumber({bsxhNum:phoneCount.value,thirdSign:'qxy',useType:1}).then((res) => {
        console.log('申请结果',res)
        if(res.success){
            ElMessage.success('提交成功')
        }else{
            ElMessage.error(res.errMsg)
        }
    }).finally(() => {
        confirmLoading.value = false
        search()
        handleApplyClose()
    }) 
}

const phoneNumberVisible = ref(false)

onMounted(async () => {
    getTableHeight()
    search()
    if (isAdmin.value) {
        searchOptionKey.value = 'NUMBER_APPLY_SEARCH_OPTIONS_FORADMIN'
        await systemService.tenantList().then(response => {
            searchConfig.value = {
                ...searchConfig.value,
                tenantIds:response.map(item => ({
                    label:item.name,
                    value:item.id
                }))
            }
        })
    }
    
})
</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';
.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}

</style>
